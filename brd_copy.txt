
1.	Document Information
1.1.	About this Document
This document outlines the business requirements identified to create a Web Application based LLM Chatbot interface for Mastercard. This chatbot interface will be accessible from mobile devices after along with web application. MasterCard user    to have enable a best-in-class Consumer experience, which drives Top of Wallet, benefit and offers by using to Retrieval Augmented Generation (RAG) from sources starting with Priceless and offering using Synthetic data    from Mastercard, ensuring that the model meets business and security standards.  
1.2.	Document Scope
This document is used for capturing user requirements in a standard format. The scope of the document covers both functional and non-functional requirement details, aligned with the RFP scope. All necessary aspects for any user requirements across all categories can be captured here. This document will support the technical team in the development of the model, while also enabling the Business Owner to verify that their business requirements have been documented completely, accurately, and unambiguously. It is important to note that the data will be used starting with <PERSON><PERSON> and offering using Synthetic data among available multiple data sources as Json in a predefined format.
1.3.	Intended Audience
The main intended audience for this document is the business owners of the proposed system. This document should be readable by the business owners, enabling them to verify that their requirements have been documented completely, accurately, and unambiguously. The solution development team will also find the information useful when designing a solution that addresses these business requirements. Additionally, the auditing team may reference this document to ensure regulatory compliance is documented.
2.	Overview
2.1.	Background
•	Fragmented Benefit Systems: Consumer benefits are often distributed across siloed platforms or channels, which makes them hard to locate and access in one centralized place. This fragmentation creates friction in the user experience. This fragmented system is not just Mastercard channels but also issuer channels. Customers don’t have easy access to both network benefits and issuer benefits.
•	Lack of Real-Time Visibility: There is limited transparency into which benefits are currently active or available. Consumers are often unaware of the full scope of what they’re entitled to, leading to underutilization.
•	Insufficient Guidance: There are no clear, intuitive instructions on how, when, or where benefits should be used to maximize value. This lack of direction results in confusion and missed opportunities.
Negative Impact on Engagement and Value:
•	Increase consumer dissatisfaction
•	Decreased benefit utilization & consumer spending
•	Missed opportunity for networks and issuers

To overcome these challenges, Tech Mahindra will build a POC demonstrating how agentic AI can be leveraged to deliver a seamless, best-in-class digital benefit presentation experience accessible through mobile and web applications by accessing benefit and offers from centralized database. Simplify benefit utilization for end consumer reflecting in Increase card spend and Increase brand affinity

2.2.	Project Objectives
Mastercard Foundry Research & Development (R&D) builds next-generation products and services that engineer the future of commerce and accelerate innovation. The objective of this POC is designing, building and testing of a modular (Agentic AI powered) benefit presentment offering to be deployed on Azure Platform. 
3.	Requirements in Scope
3.1.	Requirement Overview
MasterCard holders will log in through the Desktop Web application to view benefits and offers provided by MasterCard and banks. To   access the most relevant benefits and offers, users will be provided with a conversation AI interface leveraging GenAI. Users can interact with this chatbot with queries related to benefits and offers. The chatbot will communicate with Priceless.com and CoreBanking.com to provide the most relevant offer details eligible for the MasterCard holder.
3.2.	Functional Requirements
•	Users will be provided with a new chatbot interactive popup on the web application to facilitate queries and obtain the most relevant details regarding benefits and offers.
•	The LLM-based chatbot will use APIs from Priceless.com and Corebanking.com to retrieve details.
•	The Supervise BOT will utilize RAG in conjunction with the most relevant model to obtain the best possible benefits and offers for the Card from both the Priceless API and the core banking API.
•	The BOT will verify MasterCard eligibility and provide information exclusively related to benefits and presentment.
•	Databricks will be the primary tool for data storage & Management, and Azure services will be used for leveraging LLM-based chatbots and model serving.
•	The developed solution should be capable of updating the database with the latest data from Priceless.com and Corebanking.com at a defined frequency (once a month or every three months).
•	English is the only language used for interacting with the chatbot and receiving responses.
3.3.	Non-Functional Requirements
•	Implemented a Conversational AI Interface, ensuring the solution is scalable to integrate new data sources effectively.
•	MasterCard will provide required access to Priceless.com and CoreBanking.com APIs, along with development environments on Databricks and Azure platform services.
•	The success of an LLM-based chatbot will be measured by accuracy, cost optimisation, and quick response time.  
•	Implemented Solution should be Available 99.99% for the MasterCard users.
3.4.	Security
•	MasterCard users will log in to MasterCard desktop web applications. User authentication will be performed during login. A conversational Gen AI interface will be available to ensure that only validated users can be able to access this service.
•	The chat-based LLM solution must adhere to the established LLM data security standards to ensure its security.
•	MasterCard users will have access solely to Benefit and Presentment data that includes relevant benefits and offers associated with the card.
3.5.	Interface
•	The Intelligent Conversational GenAI interface will adhere to MasterCard design standards.
•	The interface will differentiate user responses and Bot responses using different colour codes.
•	Users can access historical data only for the current session.
•	The user will prompt a query using the interface. The LLM-based chatbot will connect with the supervisor bot to provide the query as input and receive relevant responses in the form of card benefits and the best suitable offer for the card. This information will then be displayed on the interface.
4.	 KPI and Success Metrics
•	UI Design 
•	Solution Design Document
•	Technical documentation, explaining the relevant components
•	Latency of information
•	Relevance 
•	Appropriate response   

