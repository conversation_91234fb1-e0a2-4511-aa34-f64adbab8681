Scope:
In-Scope: The architecture will be implemented on Databricks using Azure. When a user submits a query, it will be directed to the supervisor agent, which will then pass the query to either the Core banking or Priceless.com agents for answers. The process will follow the standard RAG implementation, which includes chunking the data, embedding it, retrieving relevant information from the Vector DB and generating the relevant content. Finally, the relevant information will be passed back to the user. The scope includes carrying out experiments of RAG to ensure proper question answering, relevance and latency. This will be based on Databricks on Azure.

Problem Statement

Mastercard cardholders face challenges in maximizing their benefits due to a lack of clarity and transparency around available benefits and how to access and use them. They want to discover benefits tailored to their unique needs, such as travel, entertainment, subscriptions, and curated offers. Additionally, they seek clear and simple instructions on how to utilize their benefits, see relevant benefits when they need them most, including everyday benefits, Priceless benefits, and benefits when traveling, and take action to activate or purchase benefits or services.

2.	PROPOSED SOLUTION

•	Interface Design: The interface will be a web client with a chat box, displaying questions and answers above the chat box, and differentiating between human-generated and AI-generated responses using a color-coded background.
•	Data Scope: Data will be related to two systems: Priceless.com and Core banking, restricted to these two files for the POC.
•	Caching: Robust caching mechanisms will be implemented, allowing queries to be reused within a session without retyping. The system manages session-based storage for a chatbot by using the browser's sessionStorage API to track user interactions during a single session. When a user submits a question, the system first checks the session storage to see if that question has already been asked. If it finds a match, it retrieves the stored response and displays it immediately, by passing the need to contact the server again. If the question is new, the system sends it to the server, receives the response, and then stores both the question and response in the session storage. This ensures that each unique question is only processed once per session, reducing server load and improving response speed. The session data is automatically cleared when the browser tab is closed, ensuring a fresh start for each new session.
The sessionStorage API is invoked using JavaScript in the browser. It provides a key-value storage mechanism that is accessible through windows session object. 
•	It provides a key-value storage mechanism that is accessible through windows session object.
•	The session data is automatically cleared when the browser tab is closed, ensuring a fresh start for each new session.
•	Caching in sessionStorage is client side technique. The session is destroyed when the browser is closed.
•	This can be accessed using JavaScript in browser.
•	Final Output: Implement a RAG solution to answer questions on Core banking and Priceless.com, focusing on performance & relevance.

Non-Functional Requirements
1.	Performance:
•	Latency: The system must provide real-time responses with minimal latency. The response time should meet the performance requirements specified in the acceptance criteria 
•	Scalability: For the POC it will be for 2 sources.
2.	Security:
•	User authentication: Based on Azure AD 
•	Azure services authentication using API: Based on application, which will static user and that application will be authenticated using API.
•	Databricks authentication using service principle. This will be validated.
(Detail process of backend is mentioned below)

3.	Usability:
•	User Experience: The interface must be intuitive and user-friendly, providing clear and simple instructions for users to access and utilize their benefits 
4.	Monitoring and Logging: 
           Not in scope 
5.	Integration:
•	Seamless integration with other Azure services, such as Azure Cosmos DB, enhances the overall functionality and performance of the APIs.

3.	TECHNICAL DESIGN

Flow Architecture:

 





Detailed Architecture: 







Components of Architecture:
There will be one  experiments –involving Vector DB.  Restriction to one experiment is because we have seen based on the data availablity performance is nearly the same using Graph Db as well . The major componets of the architecture are highlighted below .
1.	API:
This APIs facilitate communication and data exchange between source to data pipeline and user query/response to agentic framework on databricks.It may implemented using Rest API calls . The api call endpoints being provided  for source data are:
https://sandbox.api.mastercard.com/the-portal
/products
/products/product_id
/products/ product_id /inventories
/categories

For core banking:
Currently there is no data and for our current requirement, will be getting dummy data and the format will be similar as initial provided json file.

Hosting Environment
•	The system will be enabled by a web application deployed on azure within the precincts of MasterCard. Please refer to the architecture diagram.
•	Security: - 
•	In a secure Azure-based architecture where a chat interface interacts with Databricks and Cosmos DB, the backend plays a critical role in managing authentication, authorization, and service orchestration. Although users are authenticated via Azure Active Directory (Azure AD), the backend must still validate the JWT token to ensure its authenticity, integrity, and intended audience. This backend layer can be implemented using services like Azure App Service, Azure Functions, or Azure API Management, and it often uses Azure Managed Identity to securely access other services without storing credentials. Azure Key Vault is used to manage secrets, while Azure Monitor and Application Insights provide observability. Once the token is validated, the backend securely calls Databricks using OAuth 2.0. triggers notebook execution, and retrieves results from Cosmos DB, ensuring end-to-end security and compliance with zero-trust principles.



2.	Orchestrating Agentic Framework 
This with Integrated RAG (Retrieval Augmented Generation) is designed to enhance user security validation and streamline communication with external systems like priceless.com and core banking systems. This framework operates on Databricks within the Azure ecosystem, providing scalability, flexibility, and robust data management.
Key Components and Functions
Supervisor Agent
-	Role: Oversees the entire framework, ensuring the proper functioning of subsidiary agents.
-	Responsibilities: Manages communication between agents, coordinates tasks, and ensures security validation. It also validates user requests for access privileges to priceless.com or core banking systems.
Subsidiary Agents with Integrated RAG
-	Role: Perform specific tasks such as interacting with priceless.com and core banking systems.
-	Responsibilities: Execute tasks assigned by the supervisor agent, handle data processing, and ensure secure communication.The subsidiary agents have RAG embedded within them, enhancing their functionality in several ways:
Interaction Between Agents
•	SupervisorAgent Class:
	Validates user requests and sends messages to subsidiary agents.
	Methods: validate_user_request checks user privileges, and send_message_to_subsidiary_agents sends messages to subsidiary agents.
•	SubsidiaryAgent1 Class:
	Handles messages and queries the vector DB for data related to priceless.com.
	Method: handle_message processes messages and calls query_vector_db 
•	SubsidiaryAgent2 Class:
	Handles messages and queries the vector DB for data related to core banking systems.
•	Method: handle_message processes messages and calls query_vector_db Initialization and Registration:
	Agents are initialized and registered using the Langraph framework preffered on Databricks.
The agents will be deployed using notebooks tied with a cluster 
Query Preprocessing at Agentic Framework
The preprocessing of queries and handling within the Agentic framework involves several steps to optimize query performance and ensure accurate results:
•	Query Optimization:
	It determines the context of the query (priceless.com or core banking), and validate user access to sources.
•	Caching:
•	Robust caching mechanisms will be implemented, allowing queries to be reused within a session without retyping. The system manages session-based storage for a chatbot by using the browser's sessionStorage API to track user interactions during a single session. When a user submits a question, the system first checks the session storage to see if that question has already been asked. If it finds a match, it retrieves the stored response and displays it immediately, by reducing the need to communicate with backend server again. If the question is new, the system sends it to the server, receives the response, and then stores both the question and response in the session storage. This ensures that each unique question is only processed once per session, reducing server load and improving response speed. The session data is automatically cleared when the browser tab is closed, ensuring a fresh start for each new session.
he sessionStorage API is invoked using JavaScript in the browser. It provides a simple key-value storage mechanism that is accessible through windows session object.

•	Security
•	Authentication is taken care at three different stages. 
•	At level 1 - User authentication takes place – using mastercard approved method or Basic Authentication.
•	At level 2 – Authentication is done for every api request by passing secure credentials from UI Application to App Service Backend. 
•	The UI will send a request to the Azure App service using REST Api method, by passing secure credentials as Basic authentication parameters
•	At level 3 – Azure service will send the request to Azure databricks using service principles by passing provided input from UI along with secure credentials.

EXISTING CONTENT: 
•	In a secure Azure-based architecture where a chat interface interacts with Databricks and Cosmos DB, the backend plays a critical role in managing authentication, authorization, and service orchestration. Although users are authenticated via Azure Active Directory (Azure AD), the backend must still validate the JWT token to ensure its authenticity, integrity, and intended audience. This backend layer can be implemented using services like Azure App Service, Azure Functions, or Azure API Management, and it often uses Azure Managed Identity to securely access other services without storing credentials. Azure Key Vault is used to manage secrets, while Azure Monitor and Application Insights provide observability. Once the token is validated, the backend securely calls Databricks using OAuth 2.0. triggers notebook execution, and retrieves results from Cosmos DB, ensuring end-to-end security and compliance with zero-trust principles.





Decision-Making Process at Agentic Framework level :
The supervisor agent uses predefined rules to map specific types of user requests to the appropriate subsidiary agent. The supervisor agent analyzes the content of user requests to determine the relevant data source using techniques like keyword matching and natural language processing (NLP) or BERT. This will be an experiment criteria.
3.	Model Serving: 
Embeds the Query



4.	Orchestration Framework :
Gets the response from vector db and combines it with the query so that it can be sent to the LLM.
5.	Data Pipeline :
To read data using HTTPS APIs and send it to Databricks data files as flattened JSON, you can set up a job in Databricks that fetches data from the API, processes it, and stores it in a structured format. Here's a step-by-step outline:
1.	Reading Data Using HTTPS APIs:
•	Create a notebook in Databricks that uses libraries like requests or http client to make HTTP GET requests to the desired API endpoint.
•	Parse the response data, typically in JSON format, and flatten it if necessary. Flattening involves converting nested JSON structures into a simpler format that can be easily stored and queried.
2.	Sending Data to Databricks Data Files:
•	Once the data is flattened, use Databricks' file system (DBFS) to store the data as JSON files. You can use libraries like pandas to convert the data into a DataFrame and then write it to DBFS using df.to_json().
3.	Creating a Job to Send Data to Vector DB Cosmos:
•	Set up another notebook in Databricks that reads the flattened JSON files from DBFS.
•	Use Azure SDKs or REST APIs to connect to Azure Cosmos DB and insert the data into the vector database. Libraries like azure-cosmos can be used to interact with Cosmos DB.
4.	Scheduling the Jobs:
•	Use Databricks' job scheduler to automate the execution of these notebooks. You can define schedules to run the jobs periodically or based on specific triggers.
•	The first job will fetch and flatten the data from the API and store it in DBFS.
•	The second job will read the stored JSON files and send the data to Azure Cosmos DB.
By leveraging Databricks' job scheduling capabilities, you can ensure that these processes run automatically at defined intervals, maintaining a seamless and efficient data pipeline.

6.	Persistent Vector Database 
The framework provides a persistent vector database (DB) which gets updated as per a scheduler from the data available from two sources. The vector DB selected will be decided during the course of the POC as available in Databricks or for another experiment. For vector DB - Cosmos DB or the DB available will be used as a managed service with integrated vector database capabilities, chosen for its performance, scalability, cost efficiency, and data consistency. Additionly, we can store source, last updated date as metadata. The vector DB will be residing in the AZURE layer. For the POC we will be going with Vector db

RAG and  LLM Models :
RAG Architecture
The architecture will implement one  experiments involving a Vector DB .The the major components being:
•	Supervisor Agent: Oversees the entire framework, ensuring the proper functioning of subsidiary agents.
•	Subsidiary Agents: Perform specific tasks such as interacting with Priceless.com and core banking systems, with RAG embedded within them 
Acceptance Criteria for Experiments
The acceptance criteria for the experiments include:
•	Latency of Information: Measuring the response time to ensure it meets performance requirements.
•	Relevance: Ensuring the retrieved information is relevant to the user's query.
•	Appropriate Response: Validating that the responses are accurate and useful 
Retrieval Techniques
Dense retrieval techniques will be used:
•	Vector DB: Using embeddings and vector similarity to retrieve semantic information.
Exposure of RAG Model for Consumption
The RAG model will be exposed for consumption through APIs. These APIs facilitate communication and data exchange between the source, data pipeline, and user query/response to the agentic framework on Databricks. The APIs may be implemented using REST API calls 
Fallback Mechanism
•	If the retrieval does not find any search results, the fallback mechanism will involve:
•	Session-Based Caching: Reusing queries within a session to provide consistent results.Details are mentioned in the caching section
•	Default Responses: Providing a default response indicating that no relevant information was found 
Agentic Communication Implementation
The agentic communication will be implemented using an orchestrating agentic framework with integrated RAG. The communication mechanism between the supervisor bot and agents will involve. This will be implemented using langraph framework.
•	Supervisor Agent: Validates user requests and sends messages to subsidiary agents.
•	Subsidiary Agents: Handle messages and query the Vector DB for data related to Priceless.com and core banking systems 
Framework and Communication Mechanism
•	Orchestrating Agentic Framework: This framework operates on Databricks within the Azure ecosystem, providing scalability, flexibility, and robust data management 
•	SupervisorAgent Class: Validates user requests and sends messages to subsidiary agents. Methods include validate_user_request to check user privileges, and send_message_to_subsidiary_agents to send messages to subsidiary agents 
•	SubsidiaryAgent1 Class: Handles messages and queries the Vector DB for data related to Priceless.com. Method handle_message processes messages and calls query_vector_db 
•	SubsidiaryAgent2 Class: Handles messages and queries the Vector DB for data related to core banking systems. Method handle_message processes messages and calls query_vector_db 
•	Initialization and Registration: Agents are initialized and registered using Langraph framework on Databricks 

Embedding Model
The embedding model can one of the foundations models enabled within Databricks
LLM for Generating Final Responses
The LLM will be one of the foundations models enabled within Databricks
Evaluation Criteria 
The evaluation criteria will include:
•	Latency: Measuring response times 
•	Relevance: Ensuring the retrieved information is relevant 
•	Accuracy: Validating the accuracy of the responses 
Techniques to check for Accuracy of RAG Output
To ensure the accuracy of the RAG output, the following techniques will be employed:
The RAGAS framework will be deployed  with ( precision, Response Relevancy, Faithfulness) as parameters.
Also, Rating of response generated by RAG on scale of 1-5 by using LLM as a Judge will be looked at.

Reranking Strategy
Semantic reranking strategy will be used:
•	Similarity Scoring: Use embedding simlarity from model and rerank on semantic closeness.
•	Performance Metrics: Using performance metrics to adjust the ranking of the results 
Technical stack:
Task	Technical Stack
UI Design	Figma
UI Development	ReactJS, Tailwind CSS
Solution implementation
API Calls	Databricks REST API will be invoked 
Agentic AI Framework	Agentic Framework caph 
Model Embeddings/Model	Azure databricks embeddings /Azure databricks foundation models
Vector DB (Embedding Search)	Azure Cosmos DB or the vector model available


4.	IMPLEMENTATION PLAN

Development Process:
•	Agile methodology with iterative development and continuous integration.
Testing Plan:
•	Since it is a POC – testing will involve ensuring all components work together and the output from RAG is an expected in the performance criteria.
Deployment Plan:
•	Deployed on Azure.
5.	IMPACT ANALYSIS

Benefits:
-	Get relevant and appropriate real time response to user queries related to Benefits and Offer
Risks:
•	Potential data privacy issues.
•	System performance under high loads.
•	Integration challenges with existing systems.
Dependencies:
•	Json files on products and core banking.
•	Access to Databricks on Azure.




6.	GLOSSARY
•	RAG: Retrieval-Augmented Generation.
•	PII: Personally Identifiable Information.
•	API: Application Programming Interface.
•	UAT: User Acceptance Testing.
